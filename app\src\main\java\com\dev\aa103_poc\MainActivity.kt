package com.dev.aa103_poc

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.dev.aa103_poc.ui.auth.AuthGate
import com.dev.aa103_poc.ui.signin.SignInActivity
import com.dev.aa103_poc.ui.theme.AA103_POCTheme
import com.google.firebase.auth.FirebaseAuth

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // Get the shared instance of the FirebaseAuth object
        val auth = FirebaseAuth.getInstance()

        setContent {
            AA103_POCTheme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    // Check if user is currently signed in
                    // If null → show SignInScreen (email/password)
                    // If not null → navigate to ProjectsScreen
                    AuthGate(
                        auth = auth,
                        signedInContent = {
                            // User is signed in, show projects screen
                            // This is handled by AuthGate's default implementation
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun MainScreen(modifier: Modifier = Modifier) {
    val context = LocalContext.current

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(24.dp),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "Welcome to AA103 POC",
            modifier = Modifier.padding(bottom = 32.dp)
        )

        Button(
            onClick = {
                val intent = Intent(context, SignInActivity::class.java)
                context.startActivity(intent)
            }
        ) {
            Text("Go to Sign In")
        }
    }
}

@Preview(showBackground = true)
@Composable
fun MainScreenPreview() {
    AA103_POCTheme {
        MainScreen()
    }
}