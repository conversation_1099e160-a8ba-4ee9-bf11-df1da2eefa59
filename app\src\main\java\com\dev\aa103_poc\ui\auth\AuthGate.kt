package com.dev.aa103_poc.ui.auth

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewmodel.compose.viewModel
import com.dev.aa103_poc.ui.projects.ProjectsScreen
import com.dev.aa103_poc.ui.signin.SignInScreen
import com.dev.aa103_poc.ui.signin.SignInViewModel
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.ktx.Firebase
import com.google.firebase.auth.ktx.auth

@Composable
fun AuthGate(
    auth: FirebaseAuth = Firebase.auth,
    signedInContent: @Composable () -> Unit = {
        ProjectsScreen(
            onSignOut = {
                auth.signOut()
            }
        )
    }
) {
    var user by remember { mutableStateOf(auth.currentUser) }

    DisposableEffect(Unit) {
        val authStateListener = FirebaseAuth.AuthStateListener { firebaseAuth ->
            user = firebaseAuth.currentUser
        }
        auth.addAuthStateListener(authStateListener)
        onDispose { 
            auth.removeAuthStateListener(authStateListener) 
        }
    }

    if (user == null) {
        SignInScreen(
            viewModel = viewModel<SignInViewModel>()
        )
    } else {
        signedInContent()
    }
}
