-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:1-33:12
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:1-33:12
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:1-33:12
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:1-33:12
MERGED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:17:1-75:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\dff9eeb49628851a967b3c4d9dd34d6d\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-analytics:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1ebf595edeba579e73d2757e7829bf0\transformed\firebase-analytics-23.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:17:1-39:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\12cdd995677df0126d0a462b80fa9040\transformed\play-services-measurement-sdk-23.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\342edefa06f3a9a15c3a83dbdd6acd2e\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\17bfbcbe65f58b08aa50d65ee4be79ff\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\c094d78b5c981bb3500f9596c4e8b74a\transformed\recaptcha-18.6.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e7a69a3379df45569c3546f558bb973a\transformed\integrity-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bbf8d6b02da75a2ab29b1a721776e52d\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c850e79192fa3f4191e61be0b870439e\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ed1cff3d7d4422b4f1eaf050f6682da\transformed\play-services-measurement-impl-23.0.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\af731b7510df770256a885e2140f7b73\transformed\play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\6ed69aeef7f43bb499f70c11fcb15062\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\3657013a6d71fe1dd6afb96a9f062573\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6299a8bcc43eb16c09bc0190942333f9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\651ec013401f52383aed1782e5d67d1f\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\82fcda53b1c80edfd303116e55fceb53\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\8b7b21da48c1f3a2983580d415c28d61\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\08a2bb0f34a7779457ee489d69d98dee\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\505c72c48669dd7979e43e4933613b6a\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\56964599d331fe632618ba9789758c12\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\3fd9615b93b0adff06e98944a1d9530c\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\6e6ff7cac1c8180693000c4cb4c781c2\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\1b390323008289e38c9fa80949a8165b\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\653b08721606bb1c5b748bbe9bf58b59\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\870d682ef991f070f19daa0416b532e3\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\526cf3756fa83e1a5c93b669c29945bd\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\d1b53eb7b36aeb9ad963f5fe59240a78\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\00d3ce47580078d327a5bb81438731bc\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\b4cfa21fc15f88592fae41a1ae21d581\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\c104a2525d66b792c64e8fe5b983accd\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2d0ec3311e104e5c878391d71ed0bbf3\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\2976ef3eab492bf6d3c1aa6afa0cb458\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\fc31b96de19a919d9f3ed552e604a1fc\transformed\lifecycle-livedata-core-ktx-2.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\e7a58e0959d4480151b728150d8ad394\transformed\lifecycle-livedata-2.8.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\b914d61adb1b243a0916da67fe259b72\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c32fac05b7e36a1c02864df7162ddafd\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\e8b1009a84c0facb566143cbe496a005\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\b061bd927933a2b1052283d3e1f47409\transformed\lifecycle-viewmodel-2.8.6\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\47151b7335133b1bb89f85ea1b6355b5\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\651153abe71148b82ea8f4c675903556\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\a3bd890fd40abf3bed6f7ba86a51162a\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\94a9049912cd0dc07b237a523cd3210d\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\6d28bf15b10500656cd899ccd5321681\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\5e57f3d6c9ab84d6310eabc339fddb8e\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\5482bc31b8a63177273e2e0fd8e740e3\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ac3ff4597ab68f30febd20a6e90f1da5\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\e1c9c2af1a84de4e8b8dfce76750608f\transformed\activity-1.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\23710644ccaf4b4e74e838962d011faf\transformed\activity-ktx-1.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\946369b8970883e14ce2e344dbde0249\transformed\activity-compose-1.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\b01b64d39a296da276f52616aad1f8b3\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2fefa22ec4d57036f51f4e83fa614ea1\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-4\1ce43d5b2e2676ef3d90c81ee8880724\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-4\18c122e2de3fe41ce84c4b4465b90dbb\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\6cd211c4534375348ebe0e15bb55f5d8\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\53026ee1a502034b202cbcdf3dd2e41d\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\7b6f0a13594ca06f25f9c16e0552948b\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-4\c80413dc5210905f4ead9dab55c631f7\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8df6cfe9b523fd78e8201351aa730cd7\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\3f071fdaf0880346d7edbe0e76a8764b\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\5181197167cbd5f742aaef6158fab4d0\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9e66a2a9661fb6f5a4454f75d615a91\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\563190217cf567567e06e3adc31f2e1a\transformed\play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa6a4e029430c4cc9a0ef747c2e323ff\transformed\play-services-measurement-base-23.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a8970d54739be61e1d96257cffb187e7\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\853d721c3114da9c28d52984f4f20bfc\transformed\play-services-basement-18.5.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0c50768ee6b182ea675d1472ee7ac0d0\transformed\fragment-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\59c0cd41192e6ae5838f3269d82f4458\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\514b23bad937476d0d59cd00339da2d7\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abd0a222e4dfc87e8a9bc3a03da1fbec\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a33ceb110d2f1b63320d043dcbe18a30\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\25d17a45fa3db5de113e0f2fcaca06c9\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2d08da4a493ef086fd6834590ba089\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\391362288bf4e71b95a0a8ed0d09ce0c\transformed\googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\a43b4f13217356f38e5d09fe78ffca20\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4b501774713670cc6fcc6560c0a41a7e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\da95b38fe9667d6cb84651118af58287\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d5e3442ed7c95cf89272f7180bfc1a84\transformed\firebase-components-19.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c656546cb64cdc58fce9c6ad6917f87\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f98fe53dea6462bba0db5cf3dfa4a2b9\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b3a2f985ec22b52ffcb560d728e49a1a\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\736299c9a5dd9b982f36233a5af28f17\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfc461bb1b90db51d440c5c5cb448f77\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\fea84a01de8c234fe122e7f75e47fe4d\transformed\core-common-2.0.3\AndroidManifest.xml:2:1-21:12
	package
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:5:5-31:19
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:5:5-31:19
MERGED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-analytics:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1ebf595edeba579e73d2757e7829bf0\transformed\firebase-analytics-23.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1ebf595edeba579e73d2757e7829bf0\transformed\firebase-analytics-23.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\12cdd995677df0126d0a462b80fa9040\transformed\play-services-measurement-sdk-23.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\12cdd995677df0126d0a462b80fa9040\transformed\play-services-measurement-sdk-23.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e7a69a3379df45569c3546f558bb973a\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e7a69a3379df45569c3546f558bb973a\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bbf8d6b02da75a2ab29b1a721776e52d\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bbf8d6b02da75a2ab29b1a721776e52d\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c850e79192fa3f4191e61be0b870439e\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c850e79192fa3f4191e61be0b870439e\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ed1cff3d7d4422b4f1eaf050f6682da\transformed\play-services-measurement-impl-23.0.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ed1cff3d7d4422b4f1eaf050f6682da\transformed\play-services-measurement-impl-23.0.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\af731b7510df770256a885e2140f7b73\transformed\play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\af731b7510df770256a885e2140f7b73\transformed\play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\3657013a6d71fe1dd6afb96a9f062573\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\3657013a6d71fe1dd6afb96a9f062573\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2d0ec3311e104e5c878391d71ed0bbf3\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2d0ec3311e104e5c878391d71ed0bbf3\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\2976ef3eab492bf6d3c1aa6afa0cb458\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\2976ef3eab492bf6d3c1aa6afa0cb458\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ac3ff4597ab68f30febd20a6e90f1da5\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ac3ff4597ab68f30febd20a6e90f1da5\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-4\18c122e2de3fe41ce84c4b4465b90dbb\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-4\18c122e2de3fe41ce84c4b4465b90dbb\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8df6cfe9b523fd78e8201351aa730cd7\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8df6cfe9b523fd78e8201351aa730cd7\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\5181197167cbd5f742aaef6158fab4d0\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\5181197167cbd5f742aaef6158fab4d0\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9e66a2a9661fb6f5a4454f75d615a91\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9e66a2a9661fb6f5a4454f75d615a91\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa6a4e029430c4cc9a0ef747c2e323ff\transformed\play-services-measurement-base-23.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa6a4e029430c4cc9a0ef747c2e323ff\transformed\play-services-measurement-base-23.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a8970d54739be61e1d96257cffb187e7\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a8970d54739be61e1d96257cffb187e7\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\853d721c3114da9c28d52984f4f20bfc\transformed\play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\853d721c3114da9c28d52984f4f20bfc\transformed\play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\a43b4f13217356f38e5d09fe78ffca20\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\a43b4f13217356f38e5d09fe78ffca20\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c656546cb64cdc58fce9c6ad6917f87\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c656546cb64cdc58fce9c6ad6917f87\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\fea84a01de8c234fe122e7f75e47fe4d\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\fea84a01de8c234fe122e7f75e47fe4d\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:12:9-35
	android:label
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:10:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:8:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:11:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:14:9-29
	android:icon
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:9:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:6:9-35
	android:theme
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:13:9-47
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:7:9-65
activity#com.dev.aa103_poc.MainActivity
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:15:9-25:20
	android:label
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:18:13-45
	android:exported
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:17:13-36
	android:theme
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:19:13-51
	android:name
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:16:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:20:13-24:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:21:17-69
	android:name
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:21:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:23:17-77
	android:name
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:23:27-74
activity#com.dev.aa103_poc.ui.signin.SignInActivity
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:26:9-30:54
	android:label
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:29:13-36
	android:exported
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:28:13-37
	android:theme
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:30:13-51
	android:name
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:27:13-53
uses-sdk
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
MERGED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:21:5-23:151
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\dff9eeb49628851a967b3c4d9dd34d6d\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\dff9eeb49628851a967b3c4d9dd34d6d\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-analytics:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1ebf595edeba579e73d2757e7829bf0\transformed\firebase-analytics-23.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b1ebf595edeba579e73d2757e7829bf0\transformed\firebase-analytics-23.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\12cdd995677df0126d0a462b80fa9040\transformed\play-services-measurement-sdk-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\12cdd995677df0126d0a462b80fa9040\transformed\play-services-measurement-sdk-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\342edefa06f3a9a15c3a83dbdd6acd2e\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\342edefa06f3a9a15c3a83dbdd6acd2e\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\17bfbcbe65f58b08aa50d65ee4be79ff\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\17bfbcbe65f58b08aa50d65ee4be79ff\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\c094d78b5c981bb3500f9596c4e8b74a\transformed\recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\c094d78b5c981bb3500f9596c4e8b74a\transformed\recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e7a69a3379df45569c3546f558bb973a\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e7a69a3379df45569c3546f558bb973a\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bbf8d6b02da75a2ab29b1a721776e52d\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bbf8d6b02da75a2ab29b1a721776e52d\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c850e79192fa3f4191e61be0b870439e\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c850e79192fa3f4191e61be0b870439e\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ed1cff3d7d4422b4f1eaf050f6682da\transformed\play-services-measurement-impl-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ed1cff3d7d4422b4f1eaf050f6682da\transformed\play-services-measurement-impl-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\af731b7510df770256a885e2140f7b73\transformed\play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\af731b7510df770256a885e2140f7b73\transformed\play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\6ed69aeef7f43bb499f70c11fcb15062\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\6ed69aeef7f43bb499f70c11fcb15062\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\3657013a6d71fe1dd6afb96a9f062573\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\3657013a6d71fe1dd6afb96a9f062573\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6299a8bcc43eb16c09bc0190942333f9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6299a8bcc43eb16c09bc0190942333f9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\651ec013401f52383aed1782e5d67d1f\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\651ec013401f52383aed1782e5d67d1f\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\82fcda53b1c80edfd303116e55fceb53\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\82fcda53b1c80edfd303116e55fceb53\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\8b7b21da48c1f3a2983580d415c28d61\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\8b7b21da48c1f3a2983580d415c28d61\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\08a2bb0f34a7779457ee489d69d98dee\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\08a2bb0f34a7779457ee489d69d98dee\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\505c72c48669dd7979e43e4933613b6a\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\505c72c48669dd7979e43e4933613b6a\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\56964599d331fe632618ba9789758c12\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\56964599d331fe632618ba9789758c12\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\3fd9615b93b0adff06e98944a1d9530c\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\3fd9615b93b0adff06e98944a1d9530c\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\6e6ff7cac1c8180693000c4cb4c781c2\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\6e6ff7cac1c8180693000c4cb4c781c2\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\1b390323008289e38c9fa80949a8165b\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\1b390323008289e38c9fa80949a8165b\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\653b08721606bb1c5b748bbe9bf58b59\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\653b08721606bb1c5b748bbe9bf58b59\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\870d682ef991f070f19daa0416b532e3\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\870d682ef991f070f19daa0416b532e3\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\526cf3756fa83e1a5c93b669c29945bd\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\526cf3756fa83e1a5c93b669c29945bd\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\d1b53eb7b36aeb9ad963f5fe59240a78\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\d1b53eb7b36aeb9ad963f5fe59240a78\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\00d3ce47580078d327a5bb81438731bc\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\00d3ce47580078d327a5bb81438731bc\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\b4cfa21fc15f88592fae41a1ae21d581\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\b4cfa21fc15f88592fae41a1ae21d581\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\c104a2525d66b792c64e8fe5b983accd\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\c104a2525d66b792c64e8fe5b983accd\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2d0ec3311e104e5c878391d71ed0bbf3\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2d0ec3311e104e5c878391d71ed0bbf3\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\2976ef3eab492bf6d3c1aa6afa0cb458\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\2976ef3eab492bf6d3c1aa6afa0cb458\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\fc31b96de19a919d9f3ed552e604a1fc\transformed\lifecycle-livedata-core-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\fc31b96de19a919d9f3ed552e604a1fc\transformed\lifecycle-livedata-core-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\e7a58e0959d4480151b728150d8ad394\transformed\lifecycle-livedata-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\e7a58e0959d4480151b728150d8ad394\transformed\lifecycle-livedata-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\b914d61adb1b243a0916da67fe259b72\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\b914d61adb1b243a0916da67fe259b72\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c32fac05b7e36a1c02864df7162ddafd\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c32fac05b7e36a1c02864df7162ddafd\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\e8b1009a84c0facb566143cbe496a005\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\e8b1009a84c0facb566143cbe496a005\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\b061bd927933a2b1052283d3e1f47409\transformed\lifecycle-viewmodel-2.8.6\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\b061bd927933a2b1052283d3e1f47409\transformed\lifecycle-viewmodel-2.8.6\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\47151b7335133b1bb89f85ea1b6355b5\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\47151b7335133b1bb89f85ea1b6355b5\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\651153abe71148b82ea8f4c675903556\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\651153abe71148b82ea8f4c675903556\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\a3bd890fd40abf3bed6f7ba86a51162a\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\a3bd890fd40abf3bed6f7ba86a51162a\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\94a9049912cd0dc07b237a523cd3210d\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\94a9049912cd0dc07b237a523cd3210d\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\6d28bf15b10500656cd899ccd5321681\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\6d28bf15b10500656cd899ccd5321681\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\5e57f3d6c9ab84d6310eabc339fddb8e\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\5e57f3d6c9ab84d6310eabc339fddb8e\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\5482bc31b8a63177273e2e0fd8e740e3\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\5482bc31b8a63177273e2e0fd8e740e3\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ac3ff4597ab68f30febd20a6e90f1da5\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ac3ff4597ab68f30febd20a6e90f1da5\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\e1c9c2af1a84de4e8b8dfce76750608f\transformed\activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\e1c9c2af1a84de4e8b8dfce76750608f\transformed\activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\23710644ccaf4b4e74e838962d011faf\transformed\activity-ktx-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\23710644ccaf4b4e74e838962d011faf\transformed\activity-ktx-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\946369b8970883e14ce2e344dbde0249\transformed\activity-compose-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\946369b8970883e14ce2e344dbde0249\transformed\activity-compose-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\b01b64d39a296da276f52616aad1f8b3\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\b01b64d39a296da276f52616aad1f8b3\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2fefa22ec4d57036f51f4e83fa614ea1\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2fefa22ec4d57036f51f4e83fa614ea1\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-4\1ce43d5b2e2676ef3d90c81ee8880724\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-4\1ce43d5b2e2676ef3d90c81ee8880724\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-4\18c122e2de3fe41ce84c4b4465b90dbb\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-4\18c122e2de3fe41ce84c4b4465b90dbb\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\6cd211c4534375348ebe0e15bb55f5d8\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\6cd211c4534375348ebe0e15bb55f5d8\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\53026ee1a502034b202cbcdf3dd2e41d\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\53026ee1a502034b202cbcdf3dd2e41d\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\7b6f0a13594ca06f25f9c16e0552948b\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\7b6f0a13594ca06f25f9c16e0552948b\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-4\c80413dc5210905f4ead9dab55c631f7\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-4\c80413dc5210905f4ead9dab55c631f7\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8df6cfe9b523fd78e8201351aa730cd7\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8df6cfe9b523fd78e8201351aa730cd7\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\3f071fdaf0880346d7edbe0e76a8764b\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\3f071fdaf0880346d7edbe0e76a8764b\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\5181197167cbd5f742aaef6158fab4d0\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\5181197167cbd5f742aaef6158fab4d0\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9e66a2a9661fb6f5a4454f75d615a91\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9e66a2a9661fb6f5a4454f75d615a91\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\563190217cf567567e06e3adc31f2e1a\transformed\play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\563190217cf567567e06e3adc31f2e1a\transformed\play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa6a4e029430c4cc9a0ef747c2e323ff\transformed\play-services-measurement-base-23.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa6a4e029430c4cc9a0ef747c2e323ff\transformed\play-services-measurement-base-23.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a8970d54739be61e1d96257cffb187e7\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a8970d54739be61e1d96257cffb187e7\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\853d721c3114da9c28d52984f4f20bfc\transformed\play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\853d721c3114da9c28d52984f4f20bfc\transformed\play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0c50768ee6b182ea675d1472ee7ac0d0\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0c50768ee6b182ea675d1472ee7ac0d0\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\59c0cd41192e6ae5838f3269d82f4458\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\59c0cd41192e6ae5838f3269d82f4458\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\514b23bad937476d0d59cd00339da2d7\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\514b23bad937476d0d59cd00339da2d7\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abd0a222e4dfc87e8a9bc3a03da1fbec\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abd0a222e4dfc87e8a9bc3a03da1fbec\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a33ceb110d2f1b63320d043dcbe18a30\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a33ceb110d2f1b63320d043dcbe18a30\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\25d17a45fa3db5de113e0f2fcaca06c9\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\25d17a45fa3db5de113e0f2fcaca06c9\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2d08da4a493ef086fd6834590ba089\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2d08da4a493ef086fd6834590ba089\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\391362288bf4e71b95a0a8ed0d09ce0c\transformed\googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\391362288bf4e71b95a0a8ed0d09ce0c\transformed\googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\a43b4f13217356f38e5d09fe78ffca20\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\a43b4f13217356f38e5d09fe78ffca20\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4b501774713670cc6fcc6560c0a41a7e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4b501774713670cc6fcc6560c0a41a7e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\da95b38fe9667d6cb84651118af58287\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\da95b38fe9667d6cb84651118af58287\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d5e3442ed7c95cf89272f7180bfc1a84\transformed\firebase-components-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d5e3442ed7c95cf89272f7180bfc1a84\transformed\firebase-components-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c656546cb64cdc58fce9c6ad6917f87\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c656546cb64cdc58fce9c6ad6917f87\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f98fe53dea6462bba0db5cf3dfa4a2b9\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f98fe53dea6462bba0db5cf3dfa4a2b9\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b3a2f985ec22b52ffcb560d728e49a1a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b3a2f985ec22b52ffcb560d728e49a1a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\736299c9a5dd9b982f36233a5af28f17\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\736299c9a5dd9b982f36233a5af28f17\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfc461bb1b90db51d440c5c5cb448f77\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfc461bb1b90db51d440c5c5cb448f77\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\fea84a01de8c234fe122e7f75e47fe4d\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\fea84a01de8c234fe122e7f75e47fe4d\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
uses-permission#android.permission.INTERNET
ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\c094d78b5c981bb3500f9596c4e8b74a\transformed\recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\c094d78b5c981bb3500f9596c4e8b74a\transformed\recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ed1cff3d7d4422b4f1eaf050f6682da\transformed\play-services-measurement-impl-23.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ed1cff3d7d4422b4f1eaf050f6682da\transformed\play-services-measurement-impl-23.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\563190217cf567567e06e3adc31f2e1a\transformed\play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\563190217cf567567e06e3adc31f2e1a\transformed\play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:23:5-67
	android:name
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:25:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\c094d78b5c981bb3500f9596c4e8b74a\transformed\recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\c094d78b5c981bb3500f9596c4e8b74a\transformed\recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ed1cff3d7d4422b4f1eaf050f6682da\transformed\play-services-measurement-impl-23.0.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ed1cff3d7d4422b4f1eaf050f6682da\transformed\play-services-measurement-impl-23.0.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\563190217cf567567e06e3adc31f2e1a\transformed\play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\563190217cf567567e06e3adc31f2e1a\transformed\play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:24:5-79
	android:name
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:26:22-76
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:39:27-75
data
ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:41:17-44:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:43:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:42:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:44:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:53:13-63:29
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:67:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:70:17-109
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ed1cff3d7d4422b4f1eaf050f6682da\transformed\play-services-measurement-impl-23.0.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ed1cff3d7d4422b4f1eaf050f6682da\transformed\play-services-measurement-impl-23.0.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\563190217cf567567e06e3adc31f2e1a\transformed\play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\563190217cf567567e06e3adc31f2e1a\transformed\play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:25:22-65
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ed1cff3d7d4422b4f1eaf050f6682da\transformed\play-services-measurement-impl-23.0.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ed1cff3d7d4422b4f1eaf050f6682da\transformed\play-services-measurement-impl-23.0.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:40:13-87
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ed1cff3d7d4422b4f1eaf050f6682da\transformed\play-services-measurement-impl-23.0.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6ed1cff3d7d4422b4f1eaf050f6682da\transformed\play-services-measurement-impl-23.0.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9e66a2a9661fb6f5a4454f75d615a91\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d9e66a2a9661fb6f5a4454f75d615a91\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\563190217cf567567e06e3adc31f2e1a\transformed\play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\563190217cf567567e06e3adc31f2e1a\transformed\play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\563190217cf567567e06e3adc31f2e1a\transformed\play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\563190217cf567567e06e3adc31f2e1a\transformed\play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\563190217cf567567e06e3adc31f2e1a\transformed\play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\563190217cf567567e06e3adc31f2e1a\transformed\play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:27:22-79
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:34:17-139
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\c094d78b5c981bb3500f9596c4e8b74a\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\c094d78b5c981bb3500f9596c4e8b74a\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:19:17-127
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:36:17-109
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2d0ec3311e104e5c878391d71ed0bbf3\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2d0ec3311e104e5c878391d71ed0bbf3\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2d0ec3311e104e5c878391d71ed0bbf3\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\2976ef3eab492bf6d3c1aa6afa0cb458\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\2976ef3eab492bf6d3c1aa6afa0cb458\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c656546cb64cdc58fce9c6ad6917f87\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c656546cb64cdc58fce9c6ad6917f87\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\2976ef3eab492bf6d3c1aa6afa0cb458\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\2976ef3eab492bf6d3c1aa6afa0cb458\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\2976ef3eab492bf6d3c1aa6afa0cb458\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:30:17-78
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ac3ff4597ab68f30febd20a6e90f1da5\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ac3ff4597ab68f30febd20a6e90f1da5\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ac3ff4597ab68f30febd20a6e90f1da5\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-4\18c122e2de3fe41ce84c4b4465b90dbb\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-4\18c122e2de3fe41ce84c4b4465b90dbb\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-4\18c122e2de3fe41ce84c4b4465b90dbb\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8df6cfe9b523fd78e8201351aa730cd7\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8df6cfe9b523fd78e8201351aa730cd7\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8df6cfe9b523fd78e8201351aa730cd7\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8df6cfe9b523fd78e8201351aa730cd7\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\853d721c3114da9c28d52984f4f20bfc\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\853d721c3114da9c28d52984f4f20bfc\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\853d721c3114da9c28d52984f4f20bfc\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.dev.aa103_poc.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.dev.aa103_poc.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\fea84a01de8c234fe122e7f75e47fe4d\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\fea84a01de8c234fe122e7f75e47fe4d\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\fea84a01de8c234fe122e7f75e47fe4d\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\fea84a01de8c234fe122e7f75e47fe4d\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\fea84a01de8c234fe122e7f75e47fe4d\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
