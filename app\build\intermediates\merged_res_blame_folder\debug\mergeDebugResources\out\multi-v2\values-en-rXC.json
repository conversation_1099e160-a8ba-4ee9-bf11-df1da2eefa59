{"logs": [{"outputFile": "com.dev.aa103_poc.app-mergeDebugResources-3:/values-en-rXC/values-en-rXC.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56964599d331fe632618ba9789758c12\\transformed\\foundation-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,243", "endColumns": "187,186", "endOffsets": "238,425"}, "to": {"startLines": "89,90", "startColumns": "4,4", "startOffsets": "17860,18048", "endColumns": "187,186", "endOffsets": "18043,18230"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\342edefa06f3a9a15c3a83dbdd6acd2e\\transformed\\credentials-1.2.0-rc01\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,270", "endColumns": "214,215", "endOffsets": "265,481"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,320", "endColumns": "214,215", "endOffsets": "315,531"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\dff9eeb49628851a967b3c4d9dd34d6d\\transformed\\browser-1.4.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,255,454,665", "endColumns": "199,198,210,201", "endOffsets": "250,449,660,862"}, "to": {"startLines": "13,17,18,19", "startColumns": "4,4,4,4", "startOffsets": "2339,3125,3324,3535", "endColumns": "199,198,210,201", "endOffsets": "2534,3319,3530,3732"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0c54ae239a6d2f4a6fc3ec479ceb9b0e\\transformed\\core-1.13.1\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}, "to": {"startLines": "4,5,6,7,8,9,10,85", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "536,732,937,1138,1339,1546,1751,17072", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "727,932,1133,1334,1541,1746,1958,17271"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5482bc31b8a63177273e2e0fd8e740e3\\transformed\\ui-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,296,481,678,880,1067,1252,1445,1633,1820,1985,2152,2333,2518,2684,2863,3030", "endColumns": "190,184,196,201,186,184,192,187,186,164,166,180,184,165,178,166,237", "endOffsets": "291,476,673,875,1062,1247,1440,1628,1815,1980,2147,2328,2513,2679,2858,3025,3263"}, "to": {"startLines": "11,12,14,15,16,20,21,78,79,80,81,82,83,84,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1963,2154,2539,2736,2938,3737,3922,15833,16021,16208,16373,16540,16721,16906,17276,17455,17622", "endColumns": "190,184,196,201,186,184,192,187,186,164,166,180,184,165,178,166,237", "endOffsets": "2149,2334,2731,2933,3120,3917,4110,16016,16203,16368,16535,16716,16901,17067,17450,17617,17855"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6ed69aeef7f43bb499f70c11fcb15062\\transformed\\material3-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,275,492,706,923,1124,1322,1532,1771,1989,2228,2414,2614,2808,3008,3229,3457,3664,3894,4120,4349,4610,4832,5051,5272,5497,5692,5892,6110,6336,6535,6738,6943,7173,7414,7623,7824,8003,8201,8397,8587,8777,8982,9164,9350,9554,9758,9960,10162,10352,10561,10765,10972,11191,11374,11575", "endColumns": "219,216,213,216,200,197,209,238,217,238,185,199,193,199,220,227,206,229,225,228,260,221,218,220,224,194,199,217,225,198,202,204,229,240,208,200,178,197,195,189,189,204,181,185,203,203,201,201,189,208,203,206,218,182,200,197", "endOffsets": "270,487,701,918,1119,1317,1527,1766,1984,2223,2409,2609,2803,3003,3224,3452,3659,3889,4115,4344,4605,4827,5046,5267,5492,5687,5887,6105,6331,6530,6733,6938,7168,7409,7618,7819,7998,8196,8392,8582,8772,8977,9159,9345,9549,9753,9955,10157,10347,10556,10760,10967,11186,11369,11570,11768"}, "to": {"startLines": "22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4115,4335,4552,4766,4983,5184,5382,5592,5831,6049,6288,6474,6674,6868,7068,7289,7517,7724,7954,8180,8409,8670,8892,9111,9332,9557,9752,9952,10170,10396,10595,10798,11003,11233,11474,11683,11884,12063,12261,12457,12647,12837,13042,13224,13410,13614,13818,14020,14222,14412,14621,14825,15032,15251,15434,15635", "endColumns": "219,216,213,216,200,197,209,238,217,238,185,199,193,199,220,227,206,229,225,228,260,221,218,220,224,194,199,217,225,198,202,204,229,240,208,200,178,197,195,189,189,204,181,185,203,203,201,201,189,208,203,206,218,182,200,197", "endOffsets": "4330,4547,4761,4978,5179,5377,5587,5826,6044,6283,6469,6669,6863,7063,7284,7512,7719,7949,8175,8404,8665,8887,9106,9327,9552,9747,9947,10165,10391,10590,10793,10998,11228,11469,11678,11879,12058,12256,12452,12642,12832,13037,13219,13405,13609,13813,14015,14217,14407,14616,14820,15027,15246,15429,15630,15828"}}]}]}