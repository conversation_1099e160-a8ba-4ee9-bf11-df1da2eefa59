1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.dev.aa103_poc"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="25"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:25:5-67
11-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:25:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:26:5-79
12-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:26:22-76
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:25:5-68
13-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:25:22-65
14    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
14-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:26:5-110
14-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:26:22-107
15    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
15-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:25:5-79
15-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:25:22-76
16    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
16-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:26:5-88
16-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:26:22-85
17    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
17-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:27:5-82
17-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:27:22-79
18    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
18-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\c094d78b5c981bb3500f9596c4e8b74a\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
18-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\c094d78b5c981bb3500f9596c4e8b74a\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
19
20    <permission
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
21        android:name="com.dev.aa103_poc.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
21-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
22        android:protectionLevel="signature" />
22-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
23
24    <uses-permission android:name="com.dev.aa103_poc.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
25
26    <application
26-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:5:5-31:19
27        android:allowBackup="true"
27-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:6:9-35
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c54ae239a6d2f4a6fc3ec479ceb9b0e\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
29        android:dataExtractionRules="@xml/data_extraction_rules"
29-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:7:9-65
30        android:debuggable="true"
31        android:extractNativeLibs="false"
32        android:fullBackupContent="@xml/backup_rules"
32-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:8:9-54
33        android:icon="@mipmap/ic_launcher"
33-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:9:9-43
34        android:label="@string/app_name"
34-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:10:9-41
35        android:roundIcon="@mipmap/ic_launcher_round"
35-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:11:9-54
36        android:supportsRtl="true"
36-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:12:9-35
37        android:theme="@style/Theme.AA103_POC" >
37-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:13:9-47
38        <activity
38-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:15:9-25:20
39            android:name="com.dev.aa103_poc.MainActivity"
39-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:16:13-41
40            android:exported="true"
40-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:17:13-36
41            android:label="@string/app_name"
41-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:18:13-45
42            android:theme="@style/Theme.AA103_POC" >
42-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:19:13-51
43            <intent-filter>
43-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:20:13-24:29
44                <action android:name="android.intent.action.MAIN" />
44-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:21:17-69
44-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:21:25-66
45
46                <category android:name="android.intent.category.LAUNCHER" />
46-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:23:17-77
46-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:23:27-74
47            </intent-filter>
48        </activity>
49        <activity
49-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:26:9-30:54
50            android:name="com.dev.aa103_poc.ui.signin.SignInActivity"
50-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:27:13-53
51            android:exported="false"
51-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:28:13-37
52            android:label="Sign In"
52-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:29:13-36
53            android:theme="@style/Theme.AA103_POC" />
53-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:30:13-51
54        <activity
54-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:29:9-46:20
55            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
55-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:30:13-80
56            android:excludeFromRecents="true"
56-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:31:13-46
57            android:exported="true"
57-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:32:13-36
58            android:launchMode="singleTask"
58-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:33:13-44
59            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
59-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:34:13-72
60            <intent-filter>
60-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:35:13-45:29
61                <action android:name="android.intent.action.VIEW" />
61-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:36:17-69
61-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:36:25-66
62
63                <category android:name="android.intent.category.DEFAULT" />
63-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:38:17-76
63-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:38:27-73
64                <category android:name="android.intent.category.BROWSABLE" />
64-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:39:17-78
64-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:39:27-75
65
66                <data
66-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:41:17-44:51
67                    android:host="firebase.auth"
67-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:42:21-49
68                    android:path="/"
68-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:43:21-37
69                    android:scheme="genericidp" />
69-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:44:21-48
70            </intent-filter>
71        </activity>
72        <activity
72-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:47:9-64:20
73            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
73-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:48:13-79
74            android:excludeFromRecents="true"
74-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:49:13-46
75            android:exported="true"
75-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:50:13-36
76            android:launchMode="singleTask"
76-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:51:13-44
77            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
77-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:52:13-72
78            <intent-filter>
78-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:53:13-63:29
79                <action android:name="android.intent.action.VIEW" />
79-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:36:17-69
79-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:36:25-66
80
81                <category android:name="android.intent.category.DEFAULT" />
81-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:38:17-76
81-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:38:27-73
82                <category android:name="android.intent.category.BROWSABLE" />
82-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:39:17-78
82-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:39:27-75
83
84                <data
84-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:41:17-44:51
85                    android:host="firebase.auth"
85-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:42:21-49
86                    android:path="/"
86-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:43:21-37
87                    android:scheme="recaptcha" />
87-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:44:21-48
88            </intent-filter>
89        </activity>
90
91        <service
91-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:66:9-72:19
92            android:name="com.google.firebase.components.ComponentDiscoveryService"
92-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:67:13-84
93            android:directBootAware="true"
93-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:32:13-43
94            android:exported="false" >
94-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:68:13-37
95            <meta-data
95-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:69:13-71:85
96                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
96-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:70:17-109
97                android:value="com.google.firebase.components.ComponentRegistrar" />
97-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79356a7b987fbc113c7876c8b507ab1c\transformed\firebase-auth-24.0.1\AndroidManifest.xml:71:17-82
98            <meta-data
98-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:33:13-35:85
99                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
99-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:34:17-139
100                android:value="com.google.firebase.components.ComponentRegistrar" />
100-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77523b2664b1e8e99f43bad68d8b3cd5\transformed\play-services-measurement-api-23.0.0\AndroidManifest.xml:35:17-82
101            <meta-data
101-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:15:13-17:85
102                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
102-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:16:17-130
103                android:value="com.google.firebase.components.ComponentRegistrar" />
103-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:17:17-82
104            <meta-data
104-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:18:13-20:85
105                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
105-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:19:17-127
106                android:value="com.google.firebase.components.ComponentRegistrar" />
106-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\452fa323631c7213dcfd3bb6e12a9727\transformed\firebase-installations-19.0.0\AndroidManifest.xml:20:17-82
107            <meta-data
107-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:35:13-37:85
108                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
108-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:36:17-109
109                android:value="com.google.firebase.components.ComponentRegistrar" />
109-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:37:17-82
110        </service>
111
112        <receiver
112-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:29:9-33:20
113            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
113-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:30:13-85
114            android:enabled="true"
114-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:31:13-35
115            android:exported="false" >
115-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:32:13-37
116        </receiver>
117
118        <service
118-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:35:9-38:40
119            android:name="com.google.android.gms.measurement.AppMeasurementService"
119-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:36:13-84
120            android:enabled="true"
120-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:37:13-35
121            android:exported="false" />
121-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:38:13-37
122        <service
122-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:39:9-43:72
123            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
123-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:40:13-87
124            android:enabled="true"
124-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:41:13-35
125            android:exported="false"
125-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:42:13-37
126            android:permission="android.permission.BIND_JOB_SERVICE" />
126-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\00b7db3f19d8d0e01736fdeeb6956b68\transformed\play-services-measurement-23.0.0\AndroidManifest.xml:43:13-69
127        <service
127-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
128            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
128-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
129            android:enabled="true"
129-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
130            android:exported="false" >
130-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
131            <meta-data
131-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
132                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
132-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
133                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
133-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
134        </service>
135
136        <activity
136-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
137            android:name="androidx.credentials.playservices.HiddenActivity"
137-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
138            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
138-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
139            android:enabled="true"
139-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
140            android:exported="false"
140-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
141            android:fitsSystemWindows="true"
141-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
142            android:theme="@style/Theme.Hidden" >
142-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\b8252d495f73861042aea3d06784a1e6\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
143        </activity>
144        <activity
144-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
145            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
145-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
146            android:excludeFromRecents="true"
146-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
147            android:exported="false"
147-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
148            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
148-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
149        <!--
150            Service handling Google Sign-In user revocation. For apps that do not integrate with
151            Google Sign-In, this service will never be started.
152        -->
153        <service
153-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
154            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
154-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
155            android:exported="true"
155-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
156            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
156-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
157            android:visibleToInstantApps="true" />
157-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f95da28b7bef7f59abf71d8f324d4f52\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
158
159        <provider
159-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:23:9-28:39
160            android:name="com.google.firebase.provider.FirebaseInitProvider"
160-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:24:13-77
161            android:authorities="com.dev.aa103_poc.firebaseinitprovider"
161-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:25:13-72
162            android:directBootAware="true"
162-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:26:13-43
163            android:exported="false"
163-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:27:13-37
164            android:initOrder="100" />
164-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0366691c7d2896b06ac3c86df5541ec1\transformed\firebase-common-22.0.0\AndroidManifest.xml:28:13-36
165
166        <activity
166-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2d0ec3311e104e5c878391d71ed0bbf3\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
167            android:name="androidx.compose.ui.tooling.PreviewActivity"
167-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2d0ec3311e104e5c878391d71ed0bbf3\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
168            android:exported="true" />
168-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2d0ec3311e104e5c878391d71ed0bbf3\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
169
170        <provider
170-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
171            android:name="androidx.startup.InitializationProvider"
171-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
172            android:authorities="com.dev.aa103_poc.androidx-startup"
172-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
173            android:exported="false" >
173-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
174            <meta-data
174-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
175                android:name="androidx.emoji2.text.EmojiCompatInitializer"
175-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
176                android:value="androidx.startup" />
176-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c3d079737a88be34d5f44402cf105\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
177            <meta-data
177-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\2976ef3eab492bf6d3c1aa6afa0cb458\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:29:13-31:52
178                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
178-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\2976ef3eab492bf6d3c1aa6afa0cb458\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:30:17-78
179                android:value="androidx.startup" />
179-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\2976ef3eab492bf6d3c1aa6afa0cb458\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:31:17-49
180            <meta-data
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
181                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
182                android:value="androidx.startup" />
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
183        </provider>
184
185        <activity
185-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ac3ff4597ab68f30febd20a6e90f1da5\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
186            android:name="androidx.activity.ComponentActivity"
186-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ac3ff4597ab68f30febd20a6e90f1da5\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
187            android:exported="true" />
187-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ac3ff4597ab68f30febd20a6e90f1da5\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
188
189        <uses-library
189-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-4\18c122e2de3fe41ce84c4b4465b90dbb\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
190            android:name="android.ext.adservices"
190-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-4\18c122e2de3fe41ce84c4b4465b90dbb\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
191            android:required="false" />
191-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-4\18c122e2de3fe41ce84c4b4465b90dbb\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
192
193        <activity
193-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8df6cfe9b523fd78e8201351aa730cd7\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
194            android:name="com.google.android.gms.common.api.GoogleApiActivity"
194-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8df6cfe9b523fd78e8201351aa730cd7\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
195            android:exported="false"
195-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8df6cfe9b523fd78e8201351aa730cd7\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
196            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
196-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8df6cfe9b523fd78e8201351aa730cd7\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
197
198        <meta-data
198-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\853d721c3114da9c28d52984f4f20bfc\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
199            android:name="com.google.android.gms.version"
199-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\853d721c3114da9c28d52984f4f20bfc\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
200            android:value="@integer/google_play_services_version" />
200-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\853d721c3114da9c28d52984f4f20bfc\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
201
202        <receiver
202-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
203            android:name="androidx.profileinstaller.ProfileInstallReceiver"
203-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
204            android:directBootAware="false"
204-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
205            android:enabled="true"
205-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
206            android:exported="true"
206-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
207            android:permission="android.permission.DUMP" >
207-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
208            <intent-filter>
208-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
209                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
209-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
209-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
210            </intent-filter>
211            <intent-filter>
211-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
212                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
212-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
212-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
213            </intent-filter>
214            <intent-filter>
214-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
215                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
215-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
215-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
216            </intent-filter>
217            <intent-filter>
217-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
218                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
218-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
218-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\134526a9f5382f84ea50037f881d159a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
219            </intent-filter>
220        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
221        <activity
221-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\fea84a01de8c234fe122e7f75e47fe4d\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
222            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
222-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\fea84a01de8c234fe122e7f75e47fe4d\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
223            android:exported="false"
223-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\fea84a01de8c234fe122e7f75e47fe4d\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
224            android:stateNotNeeded="true"
224-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\fea84a01de8c234fe122e7f75e47fe4d\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
225            android:theme="@style/Theme.PlayCore.Transparent" />
225-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\fea84a01de8c234fe122e7f75e47fe4d\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
226    </application>
227
228</manifest>
