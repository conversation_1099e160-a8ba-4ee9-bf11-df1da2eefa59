{"logs": [{"outputFile": "com.dev.aa103_poc.app-mergeDebugResources-3:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6ed69aeef7f43bb499f70c11fcb15062\\transformed\\material3-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,301,427,550,650,744,855,1007,1125,1282,1367,1472,1572,1674,1797,1930,2040,2176,2318,2449,2653,2787,2911,3041,3175,3276,3374,3492,3623,3722,3824,3937,4075,4221,4335,4444,4520,4618,4718,4805,4902,5010,5090,5178,5276,5389,5484,5595,5685,5800,5902,6015,6147,6227,6334", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,86,96,107,79,87,97,112,94,110,89,114,101,112,131,79,106,96", "endOffsets": "170,296,422,545,645,739,850,1002,1120,1277,1362,1467,1567,1669,1792,1925,2035,2171,2313,2444,2648,2782,2906,3036,3170,3271,3369,3487,3618,3717,3819,3932,4070,4216,4330,4439,4515,4613,4713,4800,4897,5005,5085,5173,5271,5384,5479,5590,5680,5795,5897,6010,6142,6222,6329,6426"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4519,4639,4765,4891,5014,5114,5208,5319,5471,5589,5746,5831,5936,6036,6138,6261,6394,6504,6640,6782,6913,7117,7251,7375,7505,7639,7740,7838,7956,8087,8186,8288,8401,8539,8685,8799,8908,8984,9082,9182,9269,9366,9474,9554,9642,9740,9853,9948,10059,10149,10264,10366,10479,10611,10691,10798", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,86,96,107,79,87,97,112,94,110,89,114,101,112,131,79,106,96", "endOffsets": "4634,4760,4886,5009,5109,5203,5314,5466,5584,5741,5826,5931,6031,6133,6256,6389,6499,6635,6777,6908,7112,7246,7370,7500,7634,7735,7833,7951,8082,8181,8283,8396,8534,8680,8794,8903,8979,9077,9177,9264,9361,9469,9549,9637,9735,9848,9943,10054,10144,10259,10361,10474,10606,10686,10793,10890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\dff9eeb49628851a967b3c4d9dd34d6d\\transformed\\browser-1.4.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,267,378", "endColumns": "108,102,110,103", "endOffsets": "159,262,373,477"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3612,4014,4117,4228", "endColumns": "108,102,110,103", "endOffsets": "3716,4112,4223,4327"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\342edefa06f3a9a15c3a83dbdd6acd2e\\transformed\\credentials-1.2.0-rc01\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,113", "endOffsets": "163,277"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,218", "endColumns": "112,113", "endOffsets": "213,327"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56964599d331fe632618ba9789758c12\\transformed\\foundation-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,91", "endOffsets": "138,230"}, "to": {"startLines": "107,108", "startColumns": "4,4", "startOffsets": "11818,11906", "endColumns": "87,91", "endOffsets": "11901,11993"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\853d721c3114da9c28d52984f4f20bfc\\transformed\\play-services-basement-18.5.0\\res\\values-ml\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2309", "endColumns": "159", "endOffsets": "2464"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0c54ae239a6d2f4a6fc3ec479ceb9b0e\\transformed\\core-1.13.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "4,5,6,7,8,9,10,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "332,434,537,639,743,846,947,11455", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "429,532,634,738,841,942,1064,11551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5482bc31b8a63177273e2e0fd8e740e3\\transformed\\ui-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,287,386,490,580,666,767,854,942,1009,1076,1162,1249,1327,1403,1470", "endColumns": "94,86,98,103,89,85,100,86,87,66,66,85,86,77,75,66,118", "endOffsets": "195,282,381,485,575,661,762,849,937,1004,1071,1157,1244,1322,1398,1465,1584"}, "to": {"startLines": "11,12,32,33,34,38,39,96,97,98,99,100,101,102,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1069,1164,3721,3820,3924,4332,4418,10895,10982,11070,11137,11204,11290,11377,11556,11632,11699", "endColumns": "94,86,98,103,89,85,100,86,87,66,66,85,86,77,75,66,118", "endOffsets": "1159,1246,3815,3919,4009,4413,4514,10977,11065,11132,11199,11285,11372,11450,11627,11694,11813"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8df6cfe9b523fd78e8201351aa730cd7\\transformed\\play-services-base-18.5.0\\res\\values-ml\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,308,483,618,735,895,1016,1117,1219,1397,1509,1679,1811,1956,2113,2173,2238", "endColumns": "114,174,134,116,159,120,100,101,177,111,169,131,144,156,59,64,87", "endOffsets": "307,482,617,734,894,1015,1116,1218,1396,1508,1678,1810,1955,2112,2172,2237,2325"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1251,1370,1549,1688,1809,1973,2098,2203,2469,2651,2767,2941,3077,3226,3387,3451,3520", "endColumns": "118,178,138,120,163,124,104,105,181,115,173,135,148,160,63,68,91", "endOffsets": "1365,1544,1683,1804,1968,2093,2198,2304,2646,2762,2936,3072,3221,3382,3446,3515,3607"}}]}]}